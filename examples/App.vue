<!--
 * @Author: 捷捷 <EMAIL>
 * @Date: 2023-01-30 19:40:57
 * @LastEditors: 捷捷 <EMAIL>
 * @LastEditTime: 2024-01-09 11:27:50
 * @FilePath: /pc-wsg-ArticlePurchaseNoticeList-front/examples/App.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
-->
<template>
  <div id="app" style="display: flex">
    <div style="flex: 1; border: solid">
      <component :is="name" v-bind="models" />
    </div>
    <div style="width: 440px">
      <component
        :is="schemaName"
        v-bind="schemaModels"
        @commitModel="commitModel"
      />
    </div>
  </div>
</template>

<script>
import { models } from '../src/schema.json';

export default {
  data() {
    return {
      models,
      name: "pc-wsg-ArticlePurchaseNoticeList-front",
      schemaName: "pc-wsg-ArticlePurchaseNoticeList-front-schema",
      schemaModels: {
        compInfo: {
          info: {},
          models: {},
        },
        lubanSdk: this.getLubanSdk(),
      },
    };
  },
  mounted() {},
  methods: {
    commitModel(models) {
      this.schemaModels.compInfo.models = models;
      this.models = models;
    },
    getLubanSdk() {
      return {
        Page: {
          registerPageCompsValidator: () => {
            // eslint-disable-next-line no-console
            console.log("模拟注册页面组件成功，具体请在鲁班页面中调试");
          },
        },
      };
    },
  },
};
</script>
