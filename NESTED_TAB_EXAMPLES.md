# 嵌套Tab功能使用示例

## 功能概述

本组件现在支持嵌套Tab结构，可以在标题位置显示一级Tab，每个一级Tab下可以配置独立的二级Tab列表。

## 数据结构说明

### 基本配置
```javascript
{
  "isShowTitleTab": true,              // 是否显示标题Tab
  "defaultActiveTitleTab": "purchase", // 默认选中的标题Tab
  "titleTabList": [...]                // 标题Tab列表
}
```

### 完整数据结构
```javascript
{
  "titleTabList": [
    {
      "titleTabName": "采购公告",        // 标题Tab显示名称
      "titleTabKey": "purchase",        // 标题Tab唯一标识
      "noticeTabList": [               // 该标题Tab下的子Tab列表
        {
          "tabName": "招标公告",         // 子Tab名称
          "tabCategoryCode": ["001"],   // 父级栏目代码
          "tabSubsetCodes": ["001-1", "001-2"], // 子级栏目代码
          "hideTabSubsetCodes": "",     // 隐藏的子栏目代码
          "tabId": "123"               // Tab ID
        }
      ]
    }
  ]
}
```

## 样例数据

### 示例1：采购公告系统
```javascript
{
  "isShowTitleTab": true,
  "defaultActiveTitleTab": "purchase",
  "titleTabList": [
    {
      "titleTabName": "采购公告",
      "titleTabKey": "purchase",
      "noticeTabList": [
        {
          "tabName": "招标公告",
          "tabCategoryCode": ["001"],
          "tabSubsetCodes": ["001-1", "001-2"],
          "hideTabSubsetCodes": "",
          "tabId": "123"
        },
        {
          "tabName": "中标公告",
          "tabCategoryCode": ["002"],
          "tabSubsetCodes": ["002-1"],
          "hideTabSubsetCodes": "",
          "tabId": "124"
        },
        {
          "tabName": "变更公告",
          "tabCategoryCode": ["003"],
          "tabSubsetCodes": ["003-1", "003-2", "003-3"],
          "hideTabSubsetCodes": "003-3",
          "tabId": "125"
        }
      ]
    },
    {
      "titleTabName": "政策法规",
      "titleTabKey": "policy",
      "noticeTabList": [
        {
          "tabName": "国家政策",
          "tabCategoryCode": ["101"],
          "tabSubsetCodes": ["101-1", "101-2"],
          "hideTabSubsetCodes": "",
          "tabId": "201"
        },
        {
          "tabName": "地方法规",
          "tabCategoryCode": ["102"],
          "tabSubsetCodes": ["102-1"],
          "hideTabSubsetCodes": "",
          "tabId": "202"
        }
      ]
    }
  ]
}
```

### 示例2：新闻资讯系统
```javascript
{
  "isShowTitleTab": true,
  "defaultActiveTitleTab": "news",
  "titleTabList": [
    {
      "titleTabName": "新闻资讯",
      "titleTabKey": "news",
      "noticeTabList": [
        {
          "tabName": "头条新闻",
          "tabCategoryCode": ["news-001"],
          "tabSubsetCodes": ["news-001-1", "news-001-2"],
          "hideTabSubsetCodes": "",
          "tabId": "news-123"
        },
        {
          "tabName": "行业动态",
          "tabCategoryCode": ["news-002"],
          "tabSubsetCodes": ["news-002-1"],
          "hideTabSubsetCodes": "",
          "tabId": "news-124"
        }
      ]
    },
    {
      "titleTabName": "公司动态",
      "titleTabKey": "company",
      "noticeTabList": [
        {
          "tabName": "公司新闻",
          "tabCategoryCode": ["comp-001"],
          "tabSubsetCodes": ["comp-001-1"],
          "hideTabSubsetCodes": "",
          "tabId": "comp-201"
        },
        {
          "tabName": "人事变动",
          "tabCategoryCode": ["comp-002"],
          "tabSubsetCodes": ["comp-002-1", "comp-002-2"],
          "hideTabSubsetCodes": "",
          "tabId": "comp-202"
        }
      ]
    }
  ]
}
```

## 配置说明

### 1. 启用嵌套Tab功能
在"展示配置"中开启"是否展示标题Tab"

### 2. 配置标题Tab
在"标题Tab配置"中：
- 添加标题Tab项
- 设置标题Tab名称和标识
- 为每个标题Tab配置子Tab列表

### 3. 设置默认选中
在"基础配置"中设置"默认选中的标题Tab"

### 4. 子Tab配置
为每个标题Tab配置：
- 子Tab名称
- 父级栏目选择
- 子级栏目选择
- 隐藏栏目编码

## 使用效果

- **标题位置**：显示Tab切换组件，替代原有的文本标题
- **二级Tab**：根据选中的标题Tab动态显示对应的子Tab列表
- **数据联动**：切换标题Tab时，自动更新子Tab和数据内容
- **样式一致**：标题Tab样式与原有标题保持一致的视觉效果

## 兼容性

- 当 `isShowTitleTab` 为 `false` 时，组件回退到原有的单层Tab模式
- 原有的 `noticeTabList` 配置仍然有效，作为兼容性保留
- 所有原有功能和配置项保持不变
