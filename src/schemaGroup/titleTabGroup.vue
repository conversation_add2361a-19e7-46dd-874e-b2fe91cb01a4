<template>
  <div class="wrap">
    <!-- 通过传入的字段进行遍历 data 中的数组 -->
    <a-form-model
      v-for="(item, index) in list[listName]"
      :key="item.key"
      layout="horizontal"
      label-align="left"
      :model="item"
    >
      <span class="tab-item">第{{ index + 1 }}项</span>
      <div class="btn-list">
        <!-- 上移按钮 -->
        <a-button
          v-if="index"
          icon="arrow-up"
          type="default"
          @click="upListItem(index)"
        />

        <!-- 下移按钮 -->
        <a-button
          v-if="list[listName].length > 1 && index < list[listName].length - 1"
          icon="arrow-down"
          type="default"
          @click="downListItem(index)"
        />

        <!-- 删除按钮 -->
        <a-button icon="close" type="danger" @click="deleteListItem(index)" />
      </div>

      <!-- 标题Tab基本配置 -->
      <a-form-model-item
        v-for="config in schemaConfigItem"
        :key="config.props"
        :label="config.label"
        :prop="config.props"
      >
        <a-input
          v-if="config.type === 'input'"
          v-model="item[config.props]"
          :placeholder="config.label"
        />
      </a-form-model-item>

      <!-- 子Tab配置 -->
      <a-form-model-item label="子Tab配置">
        <div class="sub-tab-config">
          <div
            v-for="(subTab, subIndex) in item.noticeTabList"
            :key="subTab.key || subIndex"
            class="sub-tab-item"
          >
            <div class="sub-tab-header">
              <span>子Tab {{ subIndex + 1 }}</span>
              <a-button
                size="small"
                type="danger"
                icon="close"
                @click="deleteSubTab(index, subIndex)"
              />
            </div>

            <a-form-model-item label="子Tab名称">
              <a-input v-model="subTab.tabName" placeholder="请输入子Tab名称" />
            </a-form-model-item>

            <a-form-model-item label="父级栏目选择">
              <a-cascader
                v-model="subTab.tabCategoryCode"
                :options="optionsObj.parentCodeOptions"
                placeholder="请选择父级栏目"
                :replace-fields="{
                  title: 'name',
                  key: 'id',
                  value: 'code',
                }"
                @change="
                  (value, label, extra) =>
                    subTabCodeCascaderOnChange(
                      value,
                      label,
                      extra,
                      index,
                      subIndex
                    )
                "
              />
            </a-form-model-item>

            <a-form-model-item label="子级栏目选择">
              <a-select
                v-model="subTab.tabSubsetCodes"
                placeholder="请选择子级栏目"
                mode="multiple"
                option-filter-prop="children"
              >
                <a-select-option
                  v-for="option in subTab.childrenCodeOptions"
                  :key="option.id"
                  :value="option.code"
                >
                  {{ option.name }}
                </a-select-option>
              </a-select>
            </a-form-model-item>

            <a-form-model-item label="二级编码">
              <a-input
                v-model="subTab.secondCode"
                placeholder="隐藏栏目聚合的子栏目编码（用英文逗号分割)"
              />
            </a-form-model-item>
          </div>

          <a-button type="dashed" block @click="addSubTab(index)">
            添加子Tab
          </a-button>
        </div>
      </a-form-model-item>
    </a-form-model>

    <!-- 新增按钮 -->
    <a-button type="primary" block @click="addListItem"> 新增标题Tab </a-button>
  </div>
</template>

<script>
import { findItemByTree, splitArray } from "../tools";

export default {
  props: {
    // 配置项 config 列表
    schemaConfigItem: {
      type: Array,
      default: () => [],
    },

    // 更新 list 时用的函数
    updateTabList: {
      type: Function,
      default: () => {},
    },

    // 下拉列表对象，包含用到的下拉数据
    optionsObj: {
      type: Object,
      default: () => {},
    },

    // 对应 data 中的字段
    listName: {
      type: String,
      default: "",
    },

    siteId: {
      type: String,
      default: "",
    },

    domain: {
      type: String,
      default: "",
    },
    baseUrl: {
      type: String,
      default: "",
    },
    // 表单对象
    form: {
      type: Object,
      default: () => {},
    },
    wholeTree: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      // 包含不同高级配置的列表
      list: {
        titleTabAddList: [],
      },
    };
  },
  watch: {
    form: {
      handler(newVal, oldVal) {
        if (
          JSON.stringify(newVal?.titleTabList) !==
          JSON.stringify(oldVal?.titleTabList)
        ) {
          // 处理嵌套的 noticeTabList，将 tabSubsetCodes 字符串转换为数组
          const processedTitleTabList = (newVal.titleTabList || []).map(titleTab => ({
            ...titleTab,
            noticeTabList: (titleTab.noticeTabList || []).map(subTab => ({
              ...subTab,
              tabSubsetCodes: splitArray(subTab.tabSubsetCodes || ""),
            })),
          }));
          this.list.titleTabAddList = processedTitleTabList;
        }
      },
      immediate: true,
      deep: true,
    },
    "list.titleTabAddList": {
      handler() {
        this.updateTabList("titleTabList", this.list[this.listName]);
      },
      deep: true,
    },
  },
  mounted() {
    this.initChildSelect();
  },
  methods: {
    // 初始化子级栏目选择
    initChildSelect() {
      const titleTabList = this.form.titleTabList;
      if (titleTabList) {
        titleTabList.forEach((titleTab, titleIndex) => {
          if (titleTab.noticeTabList) {
            titleTab.noticeTabList.forEach((subTab, subIndex) => {
              if (subTab.tabCategoryCode) {
                const tabId = findItemByTree(
                  this.wholeTree,
                  subTab.tabCategoryCode
                )?.id;
                this.getChildrenTree(tabId, titleIndex, subIndex, this.domain, this.baseUrl);
              }
            });
          }
        });
      }
    },
    // 获取子集栏目
    getChildrenTree(parentId, titleIndex, subIndex, domain, baseUrl) {
      axios({
        baseURL: baseUrl,
        url: "/portal/zCategoryTreeFind",
        method: "GET",
        params: {
          siteId: this.siteId,
          parentId,
          domain,
        },
      }).then((res) => {
        this.$set(
          this.list[this.listName][titleIndex].noticeTabList[subIndex],
          "childrenCodeOptions",
          res?.data[0]?.children
        );
        this.$set(
          this.list[this.listName][titleIndex].noticeTabList[subIndex],
          "tabId",
          res?.data[0]?.id
        );
      });
    },

    // 子Tab父级变化清除子集并重新请求
    subTabCodeCascaderOnChange(value, label, extra, titleIndex, subIndex) {
      this.list[this.listName][titleIndex].noticeTabList[
        subIndex
      ].tabSubsetCodes = [];
      this.getChildrenTree(
        extra.triggerNode.eventKey,
        titleIndex,
        subIndex,
        this.domain,
        this.baseUrl
      );
    },

    // 新增标题Tab
    addListItem() {
      this.list[this.listName].push({
        titleTabName: "",
        titleTabKey: "",
        noticeTabList: [],
        key: this.getUuiD(10),
      });
    },

    // 新增子Tab
    addSubTab(titleIndex) {
      if (!this.list[this.listName][titleIndex].noticeTabList) {
        this.$set(this.list[this.listName][titleIndex], "noticeTabList", []);
      }
      this.list[this.listName][titleIndex].noticeTabList.push({
        tabName: "",
        tabCategoryCode: "",
        tabSubsetCodes: [],
        secondCode: "",
        key: this.getUuiD(10),
      });
    },

    // 删除子Tab
    deleteSubTab(titleIndex, subIndex) {
      this.list[this.listName][titleIndex].noticeTabList.splice(subIndex, 1);
    },

    // 删除标题Tab
    deleteListItem(index) {
      this.list[this.listName].splice(index, 1);
    },

    // 上移项
    upListItem(index) {
      const upItem = this.list[this.listName].splice(index, 1)[0];
      this.list[this.listName].splice(index - 1, 0, upItem);
    },

    // 下移项
    downListItem(index) {
      const downItem = this.list[this.listName].splice(index, 1)[0];
      this.list[this.listName].splice(index + 1, 0, downItem);
    },

    // 生成随机字符串
    getUuiD(len) {
      return Number(
        Math.random().toString().substr(3, len) + Date.now()
      ).toString(36);
    },
  },
};
</script>

<style scoped>
.wrap {
  padding: 20px;
}

.tab-item {
  font-weight: bold;
  color: #1890ff;
  margin-bottom: 10px;
  display: block;
}

.btn-list {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
}

.sub-tab-config {
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  padding: 16px;
  margin-top: 8px;
}

.sub-tab-item {
  border: 1px solid #f0f0f0;
  border-radius: 4px;
  padding: 12px;
  margin-bottom: 12px;
  background-color: #fafafa;
}

.sub-tab-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  font-weight: 500;
}
</style>
