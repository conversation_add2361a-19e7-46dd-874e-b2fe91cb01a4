/*
 * @Author: 安风 <EMAIL>
 * @Date: 2023-02-10 16:48:18
 * @LastEditors: 安风 <EMAIL>
 * @LastEditTime: 2023-02-10 16:48:21
 * @FilePath: /pc-wsg-ArticlePurchaseNoticeList-front/src/schema.js
 * @Description:
 */
import Component from "./schema.vue";

// 定义 install 方法，接收 Vue 作为参数。如果使用 use 注册插件，则所有的组件都将被注册
const install = function (Vue) {
  // 判断是否安装
  if (install.installed) return;

  Vue.component(Component.name, Component);
  Vue.component("pc-wsg-ArticlePurchaseNoticeList-front-schema", Component);
};

// 判断是否是直接引入文件
if (typeof window !== "undefined" && window.Vue) {
  install(window.Vue);
}
Component.install = install;

export default Component;
