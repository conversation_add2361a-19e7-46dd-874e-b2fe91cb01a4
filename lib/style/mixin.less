.clearfix() {
  zoom: 1;
  &::before,
  &::after {
    display: table;
    content: "";
  }
  &::after {
    clear: both;
  }
}

.size(@width; @height) {
  width: @width;
  height: @height;
}

.square(@size) {
  .size(@size; @size);
}

.ellipsis() {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.lineclamp(@line: 2) {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: @line;
  white-space: unset;
}

.transition(@attr) {
  transition: @attr 0.3s ease-in-out;
}
