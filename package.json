{"name": "@lego/pc-wsg-ArticlePurchaseNoticeList-front", "description": "网站群-标准-文章公告列表组件", "version": "1.1.1", "author": "安风", "thumb": "https://sitecdn.zcycdn.com/f2e-assets/78d8fe9f-35f8-48a5-aab9-40a36ecebcb4.jpg", "alias": "网站群-标准-文章公告列表组件", "type": "component", "keywords": [], "sceneType": "pc", "functionType": "内容", "tags": "", "license": "MIT", "main": "lib/index.umd.min.js", "private": false, "metadata": {"script": "leo-luban-vue-pc-component"}, "maintainers": [{"name": "ji<PERSON>ngyao", "email": "<EMAIL>"}, {"name": "gao<PERSON><PERSON>e", "email": "<EMAIL>"}], "repository": {"type": "git", "url": "*******************:f2e-cube/luban/pc-wsg-ArticlePurchaseNoticeList-front.git"}, "scripts": {"update": "node build/update.js", "dev": "zoo dev", "lib": "zoo build --target lib", "lint:es": "eslint src --ext .jsx,.js,.vue", "lint:style": "stylelint '**/*.less' --syntax less", "lint": "npm run lint:es && npm run lint:style", "lint-staged": "lint-staged", "lint:fix": "eslint src --ext .jsx,.js,.vue --fix && stylelint '**/*.less' --syntax less --fix", "preversion": "npm install && npm run lint", "version": "npm run lib && git add -A", "postversion": "git push && git push --tags"}, "files": ["lib", "src"], "lint-staged": {"src/**/*.{js,vue}": ["prettier --write", "eslint --fix", "git add"], "*.{js,vue,less,md}": ["prettier --write", "git add"]}, "dependencies": {"@zcy/pc-common-css": "^1.0.1", "@zcy/zcy-wsg-css-var-ponyfill-front": "^1.0.2", "vue": "2.6.14", "vuex": "^3.1.2"}, "devDependencies": {"@vue/babel-preset-app": "^4.1.1", "@zcy/zoo-cli-plugin-luban": "^1.0.0", "ant-design-vue": "1.7.8", "babel-eslint": "^11.0.0-beta.2", "core-js": "^3.6.2", "eslint": "^6.7.2", "eslint-config-zoo": "^1.0.0", "eslint-plugin-import": "^2.19.1", "eslint-plugin-vue": "^6.0.1", "eslint-plugin-vue-libs": "^4.0.0", "husky": "^3.1.0", "lint-staged": "^9.5.0", "mockjs": "^1.1.0", "poke-ui": "^1.2.28", "prettier": "^2.7.1", "stylelint": "^12.0.0", "stylelint-config-prettier": "^8.0.0", "stylelint-config-standard": "^19.0.0", "stylelint-declaration-block-no-ignored-properties": "^2.2.0", "vue-template-compiler": "2.6.14"}, "postcss": {"plugins": {"autoprefixer": {}}}, "browserslist": ["> 1%", "last 5 versions", "not ie <= 8"]}