<!--
 * @Author: 安风 <EMAIL>
 * @Date: 2023-02-14 10:27:39
 * @LastEditors: 捷捷 <EMAIL>
 * @LastEditTime: 2023-10-24 16:43:54
 * @FilePath: /pc-wsg-ArticlePurchaseNoticeList-front/src/schemaGroup/commonGroup.vue
 * @Description: 
-->

<!-- 普通配置组件 -->
<template>
  <a-form-model
    ref="form"
    layout="horizontal"
    label-align="left"
    :model="commonForm"
  >
    <!-- 通过配置文件生成表单 -->
    <a-form-model-item
      v-for="config in schemaConfigItem"
      :key="config.props"
      :prop="config.props"
      :label="config.label"
      :rules="config.rules"
    >
      <a-input
        v-if="config.type === 'input'"
        v-model="commonForm[config.props]"
        placeholder="请输入"
      />
      <a-select
        v-if="config.type === 'select' && !config.noLabelValue"
        v-model="commonForm[config.props]"
        :options="optionsObj[config.optionsName + 'Options']"
        placeholder="请选择"
      />
      <a-select
        v-else-if="config.type === 'select' && config.noLabelValue"
        v-model="commonForm[config.props]"
        placeholder="请选择"
        show-search
        option-filter-prop="children"
      >
        <a-select-option
          v-for="option in optionsObj[config.optionsName + 'Options']"
          :key="option.id"
          :value="option.id"
        >
          {{ option.name }}
        </a-select-option>
      </a-select>
      <a-input-number
        v-if="config.type === 'inputNumber'"
        v-model="commonForm[config.props]"
        placeholder="请输入"
      />
      <a-switch
        v-if="config.type === 'switch'"
        v-model="commonForm[config.props]"
      />
      <a-upload
        v-if="config.type === 'upload'"
        name="file"
        list-type="picture-card"
        class="avatar-uploader"
        :show-upload-list="false"
        action="/lego/api/v1/oss/upload"
        :before-upload="beforeUpload"
        @change="
          (info) => {
            handleUploadChange(info, config.props);
          }
        "
      >
        <div
          v-if="commonForm[config.props]"
          class="image-preview"
        >
          <img
            :src="commonForm[config.props]"
            alt=""
          >
          <div class="image-mask">
            <a-icon
              type="eye"
              @click.stop="handleImagePreview(commonForm[config.props])"
            />
            <a-icon
              type="delete"
              @click.stop="handleImageDel(config.props)"
            />
          </div>
        </div>
        <div v-else>
          <a-icon :type="loading ? 'loading' : 'plus'" />
          <div class="ant-upload-text">
            点击上传
          </div>
        </div>
      </a-upload>
      <a-modal
        :visible="imagePreviewVisible"
        width="800px"
        :z-index="1100"
        :footer="null"
        @cancel="handleImagePreview"
      >
        <img
          alt="example"
          style="width: 100%"
          :src="previewImageUrl"
        >
      </a-modal>
    </a-form-model-item>
  </a-form-model>
</template>
<script>
export default {
  props: {
    // 配置项 config 列表
    schemaConfigItem: {
      type: Array,
      default: () => [],
    },

    // 表单对象
    form: {
      type: Object,
      default: () => {},
    },

    // // 变化值传递函数
    // handleChange: {
    //   type: Function,
    //   default: () => {},
    // },

    // 下拉列表对象，包含用到的下拉数据
    optionsObj: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      commonForm: this.form,
      loading: false,
      imagePreviewVisible: false,
      previewImageUrl: "",
    };
  },
  computed: {},
  watch: {
    form: {
      handler(newVal, oldVal) {
        if (JSON.stringify(newVal) !== JSON.stringify(oldVal)) {
          this.commonForm = newVal;
        }
      },
      immediate: true,
      deep: true,
    },
    commonForm: {
      handler(newVal) {
        this.commonForm = newVal;
        this.$emit("handleChange", newVal);
      },
      // immediate: true,
      deep: true,
    },
  },
  mounted() {
    window.v = this;
  },
  methods: {
    beforeUpload(file) {
      // 图片上传尺寸限制
      const sizeLimit = file.size / 1024 < 500;
      if (!sizeLimit) {
        this.$message.error("上传图片大小需要在 500kb 以内");
      }
      return sizeLimit;
    },

    handleUploadChange(info, props) {
      const { file } = info;
      const { status, response } = file;
      if (status === "uploading") {
        this.loading = true;
        return;
      }
      if (status === "done") {
        const { success, data, error } = response;
        if (success) {
          this.commonForm[props] = data;
          this.loading = false;
          this.$message.success("上传成功");
        } else {
          this.loading = false;
          this.$message.error(error);
        }
      }
    },

    handleImagePreview(thumb) {
      this.imagePreviewVisible = !this.imagePreviewVisible;
      if (this.imagePreviewVisible) {
        this.previewImageUrl = thumb || "";
      } else {
        this.previewImageUrl = "";
      }
    },
    handleImageDel(props) {
      this.commonForm[props] = "";
    },
  },
};
</script>
<style lang="less">
.image-preview {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  width: 100%;
  height: 100%;

  &:hover .image-mask {
    display: flex;
  }

  > img {
    max-width: 100%;
    max-height: 86px;
  }

  .image-mask {
    display: none;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 3px;
    background-color: rgba(0, 0, 0, 0.6);
    text-align: center;
    cursor: pointer;
    // display: flex;
    align-items: center;
    justify-content: center;

    > .anticon-eye,
    > .anticon-delete {
      color: #fff;
      line-height: 24px;
      margin: 0 2px;
      font-size: 16px;
    }
  }
}
</style>
