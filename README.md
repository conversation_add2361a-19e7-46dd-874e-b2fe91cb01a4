#doc luban-pc-ceshi-daishanchu-front组件 doc#

# pc-ceshi-daishanchu-front

<iframe src="/demo/luban/pc-ceshi-daishanchu-front" width="100%" height="260px" frameborder="0"></iframe>

> 测试模版-待删除

## 🗂 目录结构

```
my-project
.
├── README.md
├── babel.config.js
├── config
├── examples
├── mockData
├── package.json
├── src
└── zoo.config.js
```

## 💻项目命令

- `npm run dev` 组件开发启动热加载
- `npm run lint`  组件eslint检测
- `npm run lib`   编译压缩并输出组件
- `npm version`   在更新 version 的时候添加了`hooks`

## 🏛开发步骤

  1. 安装依赖 `npm install`，全局安装 `npm i @zcy/zoo -g`
  2.  `npm run dev` 启动项目
  3. 编写组件
     - `/src/index.vue`目录编写组件主逻辑
     - `/style/index.less`目录编写组件样式
  4. 预览组件效果
     - `/examples/App.vue`目录默认已引入组件，调整`props`调试组件
  5. 开发完成
     - `npm version patch/minor/major` 打包组件，更新版本号
     - `git push` 更新远程仓库

## Schema 使用示例

1. [schema预览工具](http://luban.cai-inc.com/check-schema)
2. [schema使用说明](http://zoo.cai-inc.com:8888/%E9%B2%81%E7%8F%ADschema%E8%A7%84%E8%8C%83/)


## Vuex 使用示例
1. 您无需关心 vuex 注册问题，本地开发时已经默认为您进行注册，在鲁班中使用时vuex也将会自动被注册。
2. 您不能使用 vuex root 层
3. 您的vuex将会被注册为 module（nameSpaced:true） module 名称为组件名称 pc-ceshi-daishanchu-front 的小驼峰格式
4. 在mutation中，已经默认为您提供设值方法setState，使用示例如下。

```vue
<script>
import { createNamespacedHelpers } from "vuex";
import { upperCase } from './tools'
const { mapState, mapMutations } = createNamespacedHelpers(upperCase("pc-ceshi-daishanchu-front"));
export default {
  name: "pc-ceshi-daishanchu-front",
  components: {},
  // 组件props
  props: {},
  // 组件 state
  data() {
    return {};
  },
  methods:{
    ...mapMutations(["setState"]),
    setMessage(){
      this.setState({
        message: "setState使用示例"
      });
    },
  },
  computed: {
    // 获取自己组件vuex module中的state，推荐写法如下。
    ...mapState(['message']),
    
    // 获取其他组件的vuex数据，建议使用兼容写法，以免取到undefined、null值产生报错。
    ruleData(){
      const { roleComponentModule = {} } = this.$store.state;
      return roleComponentModule.ruleData
    },
    classes() {
      return [`lego-pc-ceshi-daishanchu-front`];
    }
  },
  mounted() {
    // 组件发起请求统一使用 axios,最终由搭建统一处理
    // axios("/api/district/children").then(() => {});
  }
};
</script>
```

## FAQ

[Zoo Cli](http://zoo.cai-inc.com:8080/)
