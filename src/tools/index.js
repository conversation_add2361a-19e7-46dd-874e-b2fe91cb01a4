/*
 * @Author: 安风 <EMAIL>
 * @Date: 2022-12-19 11:10:43
 * @LastEditors: 捷捷 <EMAIL>
 * @LastEditTime: 2024-01-09 20:47:54
 * @FilePath: /pc-wsg-ArticlePurchaseNoticeList-front/src/tools/index.js
 * @Description:
 */
// 组件名 转小驼峰
export const upperCase = (name) => {
  const arr = name.split("-");
  let result = "";
  arr.forEach((val, index) => {
    if (!index) {
      result += val;
    } else {
      result += initialUpperCase(val);
    }
  });
  return result;
};
// 首字母大写
const initialUpperCase = (input) =>
  input.charAt(0).toUpperCase() + input.slice(1);

export const getParams = (paramsObj, statusParamList, paramsDistrictCode) => {
  const result = {};
  result.code = paramsObj[0]?.tabCategoryCode || "";
  if (paramsObj[0]?.tabSubsetCodes || paramsObj[0]?.hideTabSubsetCodes) {
    result.subCodes = [
      ...(paramsObj[0]?.tabSubsetCodes || []),
      ...(paramsObj[0]?.hideTabSubsetCodes?.split(",") || []),
    ].filter((i) => {
      return i !== "";
    });
  }
  statusParamList.forEach((item) => {
    if (item.paramString && item.paramString.length > 0) {
      result[item.paramName] = item.paramString;
    } else {
      result[item.paramName] = item.isTrueOrFalse;
    }
  });
  paramsDistrictCode ? (result.districtCode = paramsDistrictCode) : null;
  return result;
};

//获取地址栏参数
export const getUrlParam = (name) => {
  let reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
  let r = window.location.search.substr(1).match(reg);
  if (r != null) return unescape(r[2]);
  return null;
};

//在树形结构数据中查找指定值的函数
export const findItemByTree = (tree, val) => {
  const temp = [...tree];
  while (temp.length) {
    const item = temp.shift();
    if (item.code === `${val}`) return item;
    if (item?.children?.length) {
      temp.push(...item.children);
    }
  }
  return null;
};
// 把用逗号隔开的数据变成单个字符串数组
export const splitArray = (data) => {
  let array = [];
  if (typeof data === "string") {
    array = data.split(",");
  } else {
    array = data;
  }
  const result = [];
  for (let i = 0; i < array?.length; i++) {
    const stringList = array[i].split(",").map((s) => s.trim());
    result.push(...stringList);
  }
  return result;
};
