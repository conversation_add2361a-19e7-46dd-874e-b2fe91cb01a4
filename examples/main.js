/*
 * @Author: 捷捷 <EMAIL>
 * @Date: 2023-01-30 19:40:57
 * @LastEditors: 捷捷 <EMAIL>
 * @LastEditTime: 2024-01-09 11:06:38
 * @FilePath: /pc-wsg-ArticlePurchaseNoticeList-front/examples/main.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import Vue from "vue";
import App from "./App.vue";
import store from "../src/store";
import Componet from "../src/index";
import SchemaComp from "../src/schema";
import Antd from "ant-design-vue";
import "poke-ui/lib/theme-chalk/index.css";
import "ant-design-vue/dist/antd.css";

// 注册组件库
Vue.use(Componet);
Vue.use(SchemaComp);
Vue.config.productionTip = false;
Vue.use(Antd);

new Vue({
  store,
  render: (h) => h(App),
}).$mount("#app");
