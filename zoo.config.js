/*
 * @Author: 捷捷 <EMAIL>
 * @Date: 2023-01-30 19:40:57
 * @LastEditors: 捷捷 <EMAIL>
 * @LastEditTime: 2024-01-09 11:11:19
 * @FilePath: /pc-wsg-ArticlePurchaseNoticeList-front/zoo.config.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
const externals = {
  vue: "Vue",
  "zoo-ui": "zoo",
  axios: "axios",
  vuex: "Vuex",
};
module.exports = {
  pages: {
    index: {
      entry: "examples/main.js",
      template: "examples/index.html",
      filename: "index.html",
    },
  },
  configureWebpack: {
    externals,
  },
  multifileEntry: {
    schema: "./src/schema.js",
    index: "./src/index.js",
  },
  webpack: {
    chainWebpack(config) {
      config.optimization.splitChunks();
    },
  },
};
