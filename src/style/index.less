@import "./mixin";
@import "./theme";

@prefix-pc-wsg-ArticlePurchaseNoticeList-front-cls: ~"@{lego-css-prefix}pc-wsg-ArticlePurchaseNoticeList-front";

.@{prefix-pc-wsg-ArticlePurchaseNoticeList-front-cls} {
  .purchasenotice-wrap {
    margin: 0 auto;
    position: relative;
    box-shadow: 0 2px 6px 0 rgba(0, 0, 0, 0.1);
    background-color: var(--bg-color_default);
    .notice-title {
      padding: 10px 20px;
      overflow: hidden;
      background-size: cover;
      background-image: var(--backgroundImage);
      border-bottom: 1px solid #e5e5e5;

      .title-content {
        display: flex;
        align-items: center;

        .title {
          display: inline-block;
          height: 33px;
          line-height: 33px;
          color: var(--comp-title-color, #0370d5);
        }

        .title-tabs {
          flex: 1;

          .po-tabs__header {
            margin-bottom: 0;
          }

          .po-tabs__item {
            padding: 8px 16px;
            font-size: inherit;
            font-weight: var(--title-font-weight, 500);
            color: var(--comp-title-color, #0370d5);
            border: none;
            background: none;

            &.is-active,
            &:hover {
              color: var(--tab-active-color, #0370d5) !important;
              background: rgba(3, 112, 213, 0.1);
            }
          }

          .po-tabs__active-bar {
            background-color: var(--tab-active-color, #0370d5);
            height: 3px;
          }

          .po-tabs__nav-wrap::after {
            display: none;
          }
        }

        .more {
          margin-left: auto;
          color: var(--more-color, var(--font-color_caption));
          &:hover {
            color: var(--more-hover-color, var(--article-hover-color, #0370d5));
          }
        }
        .iconfont {
          margin-right: 10px;
          color: var(--comp-title-color, #0370d5);
        }
      }
    }
    .notice-tab-list {
      width: 100%;
      padding-left: 20px;
      padding-right: 20px;
      .notice-comp {
        overflow: hidden;
        .notice-list {
          .notice-item {
            overflow: hidden;
            display: flex;
            align-items: center;
            height: 24px;
            &:first-child {
              margin-top: 16px !important;
            }
            .notice-status-normal,
            .notice-status-urgent {
              float: left;
              margin-right: 5px;
              padding: 2px 12px;
              border-radius: 2px;
              font-size: 12px;
            }
            .notice-status-normal {
              color: var(--article-level-1-color, #0370d5);
              background-color: var(--article-level-1-bg-color, #e5f1ff);
            }
            .notice-status-urgent {
              color: var(--article-level-2-color, #ff7a01);
              background-color: var(--article-level-2-bg-color, #fff0e2);
            }
            .title {
              flex: 1;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              font-size: 16px;
              color: var(--font-color_title);
              &:hover {
                color: var(--article-hover-color);
              }
              &::before {
                content: " ";
                min-width: 4px;
                width: 4px;
                height: 4px;
                background-color: var(--font-color_title);
                display: var(--dotDisplay);
                vertical-align: middle;
                margin-top: 9px;
                margin-right: 10px;
                margin-bottom: 9px;
              }
            }
            .district-name-tag,
            .purchase-method-tag {
              font-size: 16px;
              color: var(--comp-title-color, #0370d5);
            }
            .iconnew {
              float: left;
              font-size: 14px;
              margin-top: 4px;
              margin-left: 10px;
              color: red;
            }
            .pub-time {
              margin-left: 32px;
              color: #757575;
            }
            .top-tag {
              vertical-align: super;
              margin-left: 10px;
            }
          }
        }
      }
    }
    .notice-footer {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      padding-left: 20px;
      margin-top: 15px;
      line-height: 44px;
      border-top: 1px solid #f7f7f7;
      color: #a3a3a3;
      .today,
      .total,
      .validCount {
        color: #4a4a4a;
      }
    }
    .po-tabs__nav-wrap {
      .po-icon-arrow-left,
      .po-icon-arrow-right {
        color: #666;
        font-weight: 900;
        font-size: 16px;
      }
    }
    .po-tabs__item {
      &.is-active,
      &:hover {
        color: var(--tab-active-color, #0370d5) !important;
      }
    }
    .po-tabs__active-bar {
      background-color: var(--tab-active-color, #0370d5);
    }
    .po-tabs__nav-wrap::after {
      height: 0;
    }
    .po-tabs__header {
      margin-bottom: 5px;
    }
    // .fl {
    //   float: left;
    // }
    // .fr {
    //   float: right;
    // }
    .flex {
      display: flex;
    }
    .dot-text {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .noData {
      text-align: center;
      font-size: 32px;
      line-height: 150px;
      color: var(--list-noData-color, #e8f6ff);
    }
  }
}
