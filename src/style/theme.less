// Prefix
@lego-css-prefix: lego-;

:root {
  --primary-color_default: #3177fd;
  --primary-color_error: #ff3100; // 出错、失败
  --primary-color_waring: #ff7900; // 警示、强调、显示金额
  --primary-color_success: #2dc12d; // 成功、完成、启用
  --primary-color_disable: #cccccc; // 失效、过期

  // bg
  --bg-color_default: #ffffff; // 白色默认
  --bg-color_primary: var(--primary-color_default); // 主按钮背景
  --bg-color_content-bg: #f0f1f5; // 页面内容区背景色
  --bg-color_error: var(--primary-color_error);
  --bg-color_waring: var(--primary-color_waring);
  --bg-color_success: var(--primary-color_success);
  --bg-color_disable: var(--primary-color_disable);

  // bg:hover
  --bg-color_primary-hover: #1561f3; // 主按钮hover
  --bg-color_list-hover: #f6f9ff; // 列表中hover背景色

  // font
  --font-color_default: #ffffff;
  --font-color_title: #333; // 标题、正文文字颜色 （title）
  --font-color_body: #666; // 次级文字颜色 （body）
  --font-color_caption: #999; // 三级文字颜色（caption）
  --font-color_disable: var(--primary-color_disable); // disable
  --font-color_link: var(--primary-color); // link
  --font-color_error: var(--primary-color_error);
  --font-color_waring: var(--primary-color_waring);
  --font-color_success: var(--primary-color_success);
  --font-color_disable: var(--primary-color_disable);

  // border
  --border-color_box: #e4e4e4; // 输入框、复选框、单选框、分页按钮等描边
  --border-color_division: #e5e5e5; // 白色背景下的分割线颜色

  // box-shadow
  --box-shadow__default: 0 0 11px 0 rgba(223, 223, 223, 0.5);
  --box-shadow__row: 2px 2px 18px 0 rgba(207, 207, 207, 0.5);

  // linear-gradient
  --linear-gradient-1: linear-gradient(180deg, #ffffff 0%, #ffffff 100%);
}
