<!--
 * @Author: 安风 <EMAIL>
 * @Date: 2023-02-14 10:27:39
 * @LastEditors: 捷捷 <EMAIL>
 * @LastEditTime: 2024-01-09 19:32:07
 * @FilePath: /pc-wsg-ArticlePurchaseNoticeList-front/src/schemaGroup/tabListGroup.vue
 * @Description: 
-->

<!-- 高级配置组件，导航栏配置 -->
<template>
  <div class="wrap">
    <!-- 通过传入的字段进行遍历 data 中的数组 -->
    <a-form-model
      v-for="(item, index) in list[listName]"
      :key="item.key"
      layout="horizontal"
      label-align="left"
      :model="item"
    >
      <span class="tab-item">第{{ index + 1 }}项</span>
      <div class="btn-list">
        <!-- 上移按钮 -->
        <a-button
          v-if="index"
          icon="arrow-up"
          type="default"
          @click="upListItem(index)"
        />

        <!-- 下移按钮 -->
        <a-button
          v-if="list[listName].length > 1 && index < list[listName].length - 1"
          icon="arrow-down"
          type="default"
          @click="downListItem(index)"
        />

        <!-- 删除按钮 -->
        <a-button
          icon="close"
          type="danger"
          @click="deleteListItem(index)"
        />
      </div>

      <!-- 高级表单 -->
      <a-form-model-item
        v-for="config in schemaConfigItem"
        :key="config.props"
        :prop="config.props"
        :label="config.label"
        :rules="config.rules"
      >
        <a-input
          v-if="config.type === 'input'"
          v-model="item[config.props]"
          placeholder="请输入"
          @change="handleChange"
        />
        <a-cascader
          v-if="config.type === 'code-cascader'"
          v-model="item[config.props]"
          :options="optionsObj[config.optionsName + 'Options']"
          change-on-select
          :field-names="{
            label: 'name',
            value: 'code',
            children: 'children',
          }"
          @change="
            (value, selectedOptions) =>
              codeCascaderOnChange(index)(value, selectedOptions)
          "
        />
        <a-tree-select
          v-if="config.type === 'tree-select'"
          v-model="item[config.props]"
          allow-clear
          placeholder="请选择"
          :tree-data="optionsObj[config.optionsName + 'Options']"
          :replace-fields="{
            title: 'name',
            key: 'id',
            value: 'code',
          }"
          @change="
            (value, label, extra) =>
              codeCascaderOnChange(value, label, extra, index)
          "
        />
        <a-select
          v-if="config.type === 'select'"
          v-model="item[config.props]"
          placeholder="请选择"
          option-filter-prop="children"
          :mode="config.mode"
        >
          <a-select-option
            v-for="option in item[config.optionsName + 'Options']"
            :key="option.id"
            :value="option.code"
          >
            {{ option.name }}
          </a-select-option>
        </a-select>
        <a-switch
          v-if="config.type === 'switch'"
          v-model="item[config.props]"
        />
      </a-form-model-item>
    </a-form-model>

    <!-- 新增按钮 -->
    <a-button
      type="primary"
      block
      @click="addListItem"
    >
      新增项
    </a-button>
  </div>
</template>
<script>
import { findItemByTree } from "../tools";
import { splitArray } from "../tools";

export default {
  props: {
    // 配置项 config 列表
    schemaConfigItem: {
      type: Array,
      default: () => [],
    },

    // 更新 list 时用的函数
    updateTabList: {
      type: Function,
      default: () => {},
    },

    // 下拉列表对象，包含用到的下拉数据
    optionsObj: {
      type: Object,
      default: () => {},
    },

    // 对应 data 中的字段
    listName: {
      type: String,
      default: "",
    },

    siteId: {
      type: String,
      default: "",
    },

    domain: {
      type: String,
      default: "",
    },
    baseUrl: {
      type: String,
      default: "",
    },
    // 表单对象
    form: {
      type: Object,
      default: () => {},
    },
    wholeTree: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      // 包含不同高级配置的列表
      list: {
        titleTabAddList: [],
        tabAddList: [],
        paramsAddList: [],
      },
    };
  },
  watch: {
    form: {
      handler(newVal, oldVal) {
        if (
          JSON.stringify(newVal?.titleTabList) !==
          JSON.stringify(oldVal?.titleTabList)
        ) {
          this.list.titleTabAddList = [...(newVal.titleTabList || [])];
        }
        if (
          JSON.stringify(newVal?.noticeTabList) !==
          JSON.stringify(oldVal?.noticeTabList)
        ) {
          const tabSubsetCodesList = newVal.noticeTabList.map((i) => {
            return {
              ...i,
              tabSubsetCodes: splitArray(i.tabSubsetCodes),
            };
          });
          this.list.tabAddList = [...tabSubsetCodesList];
        }
        if (
          JSON.stringify(newVal?.statusParamsList) !==
          JSON.stringify(oldVal?.statusParamsList)
        ) {
          this.list.paramsAddList = [...newVal.statusParamsList];
        }
      },
      immediate: true,
      deep: true,
    },
    "list.titleTabAddList": {
      handler() {
        this.updateTabList("titleTabList", this.list[this.listName]);
      },
      deep: true,
    },
    "list.tabAddList": {
      handler() {
        this.updateTabList("noticeTabList", this.list[this.listName]);
      },
      deep: true,
    },
    "list.paramsAddList": {
      handler() {
        this.updateTabList("statusParamsList", this.list[this.listName]);
      },
      deep: true,
    },
  },
  mounted() {
    this.initChildSelect();
  },
  methods: {
    initChildSelect() {
      const noticeTabList = this.form.noticeTabList;
      if (noticeTabList) {
        noticeTabList.forEach((item, index) => {
          const tabId = findItemByTree(
            this.wholeTree,
            item.tabCategoryCode
          )?.id;
          this.getChildrenTree(tabId, index, this.domain, this.baseUrl);
        });
      }
    },

    // 生成 key
    getUuiD(randomLength) {
      return Number(
        Math.random().toString().substring(2, randomLength) + Date.now()
      ).toString(36);
    },

    // 获取子集栏目
    getChildrenTree(parentId, index, domain, baseUrl) {
      axios({
        baseURL: baseUrl,
        url: "/portal/zCategoryTreeFind",
        method: "GET",
        params: {
          siteId: this.siteId,
          parentId,
          domain,
        },
      }).then((res) => {
        this.$set(
          this.list[this.listName][index],
          "childrenCodeOptions",
          res?.data[0]?.children // 捷捷调试res?.result?.data[0]?.children   res?.data[0]?.children
        );
        this.$set(
          this.list[this.listName][index],
          "tabId",
          res?.data[0]?.id // 捷捷调试res?.result?.data[0]?.children   res?.data[0]?.children
        );
      });
    },

    // 父级变化清除子集并重新请求
    codeCascaderOnChange(value, label, extra, index) {
      this.list[this.listName][index][this.schemaConfigItem[2].props] = [];
      this.getChildrenTree(
        extra.triggerNode.eventKey,
        index,
        this.domain,
        this.baseUrl
      );
    },

    // 新增项操作 根据不同的 listName 初始化不同的数据
    addListItem() {
      if (this.listName === "titleTabAddList") {
        this.list[this.listName].push({
          [this.schemaConfigItem[0].props]: "",
          [this.schemaConfigItem[1].props]: "",
          noticeTabList: [],
          key: this.getUuiD(10),
        });
      } else if (this.listName === "tabAddList") {
        this.list[this.listName].push({
          [this.schemaConfigItem[0].props]: "",
          [this.schemaConfigItem[1].props]: [],
          [this.schemaConfigItem[2].props]: [],
          key: this.getUuiD(10),
        });
      } else if (this.listName === "paramsAddList") {
        this.list[this.listName].push({
          [this.schemaConfigItem[0].props]: "",
          [this.schemaConfigItem[1].props]: false,
          [this.schemaConfigItem[2].props]: "",
          key: this.getUuiD(10),
        });
      }
    },

    // 删除项
    deleteListItem(index) {
      this.list[this.listName].splice(index, 1);
    },

    // 上移项
    upListItem(index) {
      const upItem = this.list[this.listName].splice(index, 1)[0];
      this.list[this.listName].splice(index - 1, 0, upItem);
    },

    // 下移项
    downListItem(index) {
      const downItem = this.list[this.listName].splice(index, 1)[0];
      this.list[this.listName].splice(index + 1, 0, downItem);
    },
  },
};
</script>
