// 自定义配置项书写标准
[
  {
    groupName: "配置名称",
    groupListName:
      "需要 list 配置时，需要添加此配置，用来区分各自配置的 list，需要与组件中的字段一致",
    config: [
      {
        label: "输入项名称",
        props: "配置项字段",
        type: "表单类型（input、select等）",
        rules: "表单校验规则",

        // 下面两项可能是用来获取select的options，优先取optionsName
        interface: "如果表单中的数据依赖外部接口，则添加此项",
        optionsName:
          "用于select的选择项名称前缀（如 optionName: tab 则 options 为 tabOptions）",
        noLabelValue:
          "是否为 label 和 value 的映射，如果不是需要置为 true，默认为 false",
      },
    ],
  },
];

const schemaConfig = [
  {
    groupName: "基础配置",
    config: [
      {
        label: "组件标题",
        props: "componentTitle",
        type: "input",
        rules: [{ required: true, message: "请填写组件标题" }],
      },
      {
        label: "更多文案",
        props: "moreText",
        type: "input",
        rules: [{ required: true, message: "请填写更多文案" }],
      },
      {
        label: "new标签显示的时间范围（0不展示new标签）",
        props: "newTagTime",
        type: "inputNumber",
        rules: [
          { required: true, message: "请填写new标签显示的时间范围" },
          { required: true, type: "number", message: "时间范围为数字" },
        ],
      },
      {
        label: "默认显示的导航栏",
        props: "defaultActiveTab",
        type: "input",
      },
      {
        label: "组件展示文章数量",
        props: "showPageSize",
        type: "inputNumber",
        rules: [
          { required: true, message: "请填写组件展示文章数量" },
          { required: true, type: "number", message: "文章数量为数字" },
        ],
        optionsName: "tab",
      },
      {
        label: "二级页链接地址",
        props: "secondLevelPageHref",
        type: "input",
        rules: [{ required: true, message: "请填写二级页链接地址" }],
      },
      {
        label: "详情页链接地址",
        props: "detailPageHref",
        type: "input",
        rules: [{ required: true, message: "请填写详情页链接地址" }],
      },
      {
        label: "标题前icon的类名",
        props: "titleIconfontClassName",
        type: "input",
      },
      {
        label: "区划Id（当区分省级和市县时需要传入这个区划Id）",
        props: "paramsDistrictCode",
        type: "input",
      },
      {
        label: "组件标题背景图片",
        props: "backgroundImageUrl",
        type: "upload",
      },
      {
        label: "滚动速度",
        props: "rollingSpeed",
        type: "select",
        optionsName: "rollingSpeed",
      },
    ],
  },
  {
    groupName: "样式配置",
    config: [
      {
        label: "组件宽度（单位px）",
        props: "componentWidth",
        type: "input",
        rules: [{ required: true, message: "请填写组件宽度" }],
      },
      {
        label: "组件高度（单位px）",
        props: "componentHeight",
        type: "input",
        rules: [{ required: true, message: "请填写组件高度" }],
      },
      {
        label: "大标题字体粗度（可填400-800）",
        props: "titleFontWeight",
        type: "select",
        optionsName: "fontWeight",
      },
      {
        label: "组件标题字体大小",
        props: "titleFontSize",
        type: "select",
        optionsName: "titleFontSize",
      },
      {
        label: "文章字体大小",
        props: "articleTitleFontSize",
        type: "select",
        optionsName: "articleTitleFontSize",
      },
      {
        label: "标题icon大小",
        props: "iconFontSize",
        type: "select",
        optionsName: "iconFontSize",
      },
      {
        label: "更多字体大小",
        props: "moreFontSize",
        type: "select",
        optionsName: "moreFontSize",
      },
      {
        label: "文章标题行间距",
        props: "lineSpacing",
        type: "select",
        optionsName: "articleLineHeight",
      },
      {
        label: "文章标题上外边距",
        props: "upSpacing",
        type: "select",
        optionsName: "articleMarginTop",
      },
      {
        label: "置顶标签颜色类型",
        props: "topUpLabelType",
        type: "select",
        optionsName: "topUpLabelType",
      },
      {
        label: "置顶标签文字",
        props: "topUpLabelText",
        type: "input",
      },
    ],
  },
  {
    groupName: "展示配置",
    config: [
      {
        label: "是否展示底部数量",
        props: "isShowFooter",
        type: "switch",
      },
      {
        label: "是否展示底部今日新增数据",
        props: "isShowToday",
        type: "switch",
      },
      {
        label: "是否展示底部有效数据",
        props: "isShowValid",
        type: "switch",
      },
      {
        label: "是否展示底部累计数据",
        props: "isShowTotal",
        type: "switch",
      },
      {
        label: "是否展示导航",
        props: "isShowTab",
        type: "switch",
      },
      {
        label: "是否展示区划",
        props: "isShowDistrictName",
        type: "switch",
      },
      {
        label: "是否展示采购方式",
        props: "isShowPurchaseMethod",
        type: "switch",
      },
      {
        label: "是否展示标题前的icon",
        props: "isShowTitleIconfont",
        type: "switch",
      },
      {
        label: "是否展示文章状态",
        props: "isShowArticleStatus",
        type: "switch",
      },
      {
        label: "是否展示发布时间",
        props: "isShowPubTime",
        type: "switch",
      },
      {
        label: "是否标题滚动",
        props: "isRollTitle",
        type: "switch",
      },
      {
        label: "是否鼠标悬浮停止滚动",
        props: "isHoverStopRolling",
        type: "switch",
      },
      {
        label: "是否开启置顶标签展示",
        props: "isShowTopUpLabel",
        type: "switch",
      },
    ],
  },
  {
    groupName: "接口配置",
    config: [
      {
        label: "文章列表接口",
        props: "articleListInterface",
        type: "input",
        rules: [{ required: true, message: "请填写文章列表接口" }],
      },
    ],
  },
  {
    // 此处配置项逻辑在内部处理，
    groupName: "导航栏配置",
    groupListName: "tabAddList",
    config: [
      {
        label: "导航栏名称",
        props: "tabName",
        type: "input",
      },
      {
        label: "导航栏父级栏目选择",
        props: "tabCategoryCode",
        type: "tree-select",
        optionsName: "parentCode",
        interface: "/admin/category/home/<USER>", // 子树接口，捷捷修改
      },
      {
        label: "导航栏聚合子级栏目选择",
        props: "tabSubsetCodes",
        type: "select",
        optionsName: "childrenCode",
        mode: "multiple",
      },
      {
        label: "隐藏栏目聚合的子栏目编码（用英文逗号分割)",
        props: "hideTabSubsetCodes",
        type: "input",
      },
    ],
  },
  {
    // 此处配置项逻辑在内部处理，
    groupName: "状态参数列表配置",
    groupListName: "paramsAddList",
    config: [
      {
        label: "参数名",
        props: "paramName",
        type: "input",
      },
      {
        label: "参数状态（此值与字符串值只可选择一个）",
        props: "isTrueOrFalse",
        type: "switch",
      },
      {
        label: "参数字符串值（此值与状态只可选择一个）",
        props: "paramString",
        type: "input",
      },
    ],
  },
];

export default schemaConfig;
