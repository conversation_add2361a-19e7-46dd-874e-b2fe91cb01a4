<template>
  <div :class="classes">
    <div
      class="purchasenotice-wrap"
      :style="{
        width: componentWidth,
        height: componentHeight,
      }"
    >
      <div
        class="notice-title"
        :style="{
          '--backgroundImage': `url('${backgroundImageUrl}')`,
          height: titleBoxHeight,
        }"
      >
        <i
          v-if="isShowTitleIconfont"
          class="iconfont"
          :class="titleIconfontClassName"
          :style="{ fontSize: iconFontSize }"
        />
        <span
          ref="titleFontSizeRef"
          class="title"
          :style="{
            fontWeight: titleFontWeight,
            fontSize: titleFontSize,
          }"
        >{{ componentTitle }}</span>
        <a
          :href="moreHref"
          target="_blank"
          class="more"
          :style="{ fontSize: moreFontSize }"
        >{{ moreText }}></a>
      </div>
      <div
        class="notice-tab-list"
        :style="{ paddingTop: upSpacing, paddingBottom: downSpacing }"
      >
        <po-tabs
          v-if="isShowTab && noticeTabList.length"
          v-model="activeTab"
          @tab-click="tabHandleClickGetArticleData"
        >
          <po-tab-pane
            v-for="tab in noticeTabList"
            :key="tab.tabName"
            :label="tab.tabName"
            :name="tab.tabName"
          />
        </po-tabs>
        <div
          v-if="destroy"
          class="notice-comp"
          :style="{ height: noticeCompHeight }"
        >
          <ul
            v-if="articleList && articleList.length > 0"
            ref="noticeListRef"
            class="notice-list"
          >
            <!-- 状态标识 普通、紧急 -->
            <li
              v-for="title in isRollTitle
                ? articleList
                : articleList.slice(0, showPageSize)"
              :key="title.articleId"
              class="notice-item"
              :style="{ marginTop: lineSpacing }"
            >
              <span
                v-if="
                  !isShowDistrictName &&
                    isShowArticleStatus &&
                    title.urgentLevel === 1
                "
                class="notice-status-normal"
              >
                普通
              </span>
              <span
                v-if="
                  !isShowDistrictName &&
                    isShowArticleStatus &&
                    title.urgentLevel === 2
                "
                class="notice-status-urgent"
              >
                紧急
              </span>
              <!-- 区划名称 -->
              <span
                v-if="
                  !isShowArticleStatus &&
                    title.districtName &&
                    isShowDistrictName
                "
                class="district-name-tag fl"
              >[{{ title.districtName }}]&nbsp;</span>

              <!-- 采购方式 -->
              <span
                v-if="
                  !isShowArticleStatus &&
                    title.purchaseMethod &&
                    isShowPurchaseMethod
                "
                class="purchase-method-tag fl"
              >[{{ title.purchaseMethod }}]&nbsp;</span>

              <!-- 文章标题 -->
              <a
                target="_blank"
                :href="`${detailPageHref}?parentId=${noticeListObj.firstId}&articleId=${title.articleId}`"
                class="title flex"
                :style="{
                  fontSize: articleTitleFontSize,
                  '--dotDisplay':
                    isShowDistrictName ||
                    isShowPurchaseMethod ||
                    isShowArticleStatus
                      ? 'none'
                      : 'inline-block',
                }"
              >
                <div class="dot-text">{{ title.title }}</div>
                <!-- new标签 -->
                <i
                  v-if="newTagTime > 0 && isNew(title.pubDate)"
                  class="iconfont"
                  :class="isNew(title.pubDate) ? 'iconnew' : ''"
                />
                <po-tag
                  v-if="isShowTopUpLabel && title.isStickLevel"
                  class="top-tag"
                  size="small"
                  :type="topUpLabelType"
                >{{ topUpLabelText || "置顶" }}</po-tag>
              </a>

              <!-- 时间 -->
              <span
                v-if="isShowPubTime"
                class="pub-time fr"
              >
                {{ title.publishDateString }}
              </span>
            </li>
          </ul>

          <!-- 没有文章 -->
          <p
            v-else
            class="noData"
          >
            暂无数据
          </p>
        </div>
        <!-- 公告数量 -->
        <div
          v-if="isShowFooter"
          class="notice-footer"
        >
          <span
            v-if="isShowToday"
          >今日新增：<span
            class="today"
          >{{ noticeListObj.todayTotal || 0 }}&nbsp;条&nbsp;&nbsp;</span></span>
          <span
            v-if="isShowValid"
          >有效：<span
            class="validCount"
          >{{ noticeListObj.validCount || 0 }}&nbsp;条&nbsp;&nbsp;</span></span>
          <span
            v-if="isShowTotal"
          >累计：<span
            class="total"
          >{{ noticeListObj.total || 0 }}&nbsp;条&nbsp;&nbsp;</span></span>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import Vue from "vue";
import { getParams } from "./tools";
Vue.config.devtools = true;
// vuex 使用示例 请阅读项目内 README.md 文件
export default {
  name: "pc-wsg-ArticlePurchaseNoticeList-front",
  // 组件props
  props: {
    componentTitle: {
      type: String,
      default: "",
      required: false,
    },
    moreText: {
      type: String,
      default: "",
      required: false,
    },
    newTagTime: {
      type: Number,
      default: 0,
      required: false,
    },
    defaultActiveTab: {
      type: String,
      default: "",
      required: false,
    },
    showPageSize: {
      type: Number,
      default: 8,
      required: false,
    },
    rollingSpeed: {
      type: Number,
      default: 10,
      required: false,
    },
    secondLevelPageHref: {
      type: String,
      default: "",
      required: false,
    },
    detailPageHref: {
      type: String,
      default: "",
      required: false,
    },
    titleIconfontClassName: {
      type: String,
      default: "",
      required: false,
    },
    titleFontWeight: {
      type: String,
      default: "500",
      required: false,
    },
    titleFontSize: {
      type: String,
      default: "22px",
      required: false,
    },
    articleTitleFontSize: {
      type: String,
      default: "14px",
      required: false,
    },
    iconFontSize: {
      type: String,
      default: "26px",
      required: false,
    },
    moreFontSize: {
      type: String,
      default: "13px",
      required: false,
    },
    lineSpacing: {
      type: String,
      default: "12px",
      required: false,
    },
    upSpacing: {
      type: String,
      default: "6px",
      required: false,
    },
    componentWidth: {
      type: String,
      default: "",
      required: false,
    },
    componentHeight: {
      type: String,
      default: "",
      required: false,
    },
    isShowFooter: {
      type: Boolean,
      default: false,
      required: false,
    },
    isHoverStopRolling: {
      type: Boolean,
      default: false,
      required: false,
    },
    isShowTab: {
      type: Boolean,
      default: false,
      required: false,
    },
    isShowDistrictName: {
      type: Boolean,
      default: false,
      required: false,
    },
    isShowPurchaseMethod: {
      type: Boolean,
      default: false,
      required: false,
    },
    isShowTitleIconfont: {
      type: Boolean,
      default: false,
      required: false,
    },
    articleListInterface: {
      type: String,
      default: "",
      required: false,
    },
    noticeTabList: {
      type: Array,
      default: () => [],
      required: false,
    },
    paramsDistrictCode: {
      type: String,
      default: "",
      required: false,
    },
    statusParamsList: {
      type: Array,
      default: () => [],
      required: false,
    },
    backgroundImageUrl: {
      type: String,
      default: "",
      required: false,
    },
    isShowToday: {
      type: Boolean,
      default: false,
      required: false,
    },
    isRollTitle: {
      type: Boolean,
      default: false,
      required: false,
    },
    isShowValid: {
      type: Boolean,
      default: false,
      required: false,
    },
    isShowTotal: {
      type: Boolean,
      default: false,
      required: false,
    },
    isShowPubTime: {
      type: Boolean,
      default: true,
      required: false,
    },
    isShowArticleStatus: {
      type: Boolean,
      default: false,
      required: false,
    },
    topUpLabelType: {
      type: String,
      default: "",
      required: false,
    },
    topUpLabelText: {
      type: String,
      default: "",
      required: false,
    },
    isShowTopUpLabel: {
      type: Boolean,
      default: false,
      required: false,
    },
  },
  // 组件 state
  data() {
    return {
      // activeTab: "",
      noticeListObj: {}, // 用接口获取的列表数据
      titleBoxHeight: "64px",
      downSpacing: null,
      noticeCompHeight: "",
      destroy: true,
    };
  },
  computed: {
    classes() {
      return ["lego-pc-wsg-ArticlePurchaseNoticeList-front"];
    },

    // 默认选中的导航
    activeTab() {
      return this.defaultActiveTab;
    },

    // 列表数组
    articleList() {
      return this.noticeListObj.children;
    },
    realIsShowTotal() {
      return !!this.isShowTotal;
    },
    realIsShowValid() {
      return !!this.isShowValid;
    },
    realIsShowToday() {
      return !!this.isShowToday;
    },
    // 更多链接
    moreHref() {
      const parentId = this.noticeListObj.firstId || "";
      const code = this.noticeListObj.code || "";
      let hrefStr = "";
      this.statusParamsList.forEach((item) => {
        if (item.paramName && item.paramName.length > 0) {
          if (item.paramString && item.paramString.length > 0) {
            hrefStr += `${item.paramName}=${item.paramString}&`;
          } else {
            hrefStr += `${item.paramName}=${item.isTrueOrFalse}&`;
          }
        }
      });
      if (this.paramsDistrictCode) {
        hrefStr += `districtCode=${this.paramsDistrictCode}&`;
      }
      const secondCode =
        this.noticeTabList.filter(item => item.tabName === this.activeTab)[0]
          ?.secondCode || "";
      return `${
        this.secondLevelPageHref
      }?${hrefStr}parentId=${parentId}&childrenCode=${
        secondCode ? secondCode : code
      }`;
    },
  },
  mounted() {
    this.init();
  },
  methods: {
    /**
     * @description: 初始化函数
     */
    init() {
      // 初始化的时候默认选中配置的tab栏
      this.activeTab = this.defaultActiveTab;
      this.$el.style.setProperty(
        "--hoverStatus",
        this.isHoverStopRolling ? "paused" : "running"
      );
      this.tabHandleClickGetArticleData();
      this.getTitleBoxHeight();
      this.getDownSpacing();
      this.getNoticeItemHeight();
    },
    //获取标题容器高度，根据标题字体大小调整
    getTitleBoxHeight() {
      const titleSize = parseInt(
        this.$refs.titleFontSizeRef.style.fontSize.replace("px", "")
      );
      this.titleBoxHeight = titleSize * 2 + 16 + "px";
    },

    //根据是否有tab判断下间距大小
    getDownSpacing() {
      this.downSpacing = this.isShowTab
        ? parseInt(this.upSpacing) + 10 + "px"
        : parseInt(this.upSpacing) + 16 + "px";
    },

    // 获取滚动容器高度
    getNoticeItemHeight() {
      const footerHeight = this.isShowFooter ? 45 : 0;
      const tabHeight = this.isShowTab ? 55 : 0;
      this.noticeCompHeight =
        parseInt(this.componentHeight) -
        parseInt(this.titleBoxHeight) -
        parseInt(tabHeight) -
        footerHeight +
        "px";
    },

    /**
     * @description: 判断当前是否为配置时间段新增的数据
     * @param {string} date 毫秒时间
     */
    isNew(date) {
      if (this.newTagTime !== 0) {
        return (
          new Date().getTime() - date < this.newTagTime * 24 * 60 * 60 * 1000
        );
      }
      return false;
    },

    /**
     * @description: 使用配置的接口和参数进行请求数据
     * @param {*} path 接口地址
     * @param {*} paramsArr 参数数组对象
     */
    useInterfaceGetArticleList(path, data) {
      // 手动销毁组件
      this.destroy = false;
      this.$nextTick(() => {
        // 去除 animation 属性
        this.$el.style.setProperty("--rollingAnimation", "none");
      });
      axios({
        method: "POST",
        url: path,
        data,
      }).then((res) => {
        this.destroy = true;
        this.noticeListObj = res?.result?.data || {};
        if (
          this.noticeListObj.children.length > this.showPageSize &&
          this.isRollTitle
        ) {
          this.$nextTick(() => {
            this.handleScrolling();
          });
        }
      });
    },

    tabHandleClickGetArticleData(tab = { name: this.defaultActiveTab }) {
      if (this.isShowTab) {
        // 有tab栏是传入id
        const selectTabCodeAndId = this.noticeTabList?.filter((item) => {
          if (item.tabName === tab.name) {
            return item;
          }
        });
        const payload = getParams(
          selectTabCodeAndId,
          this.statusParamsList,
          this.paramsDistrictCode
        );
        payload.pageSize = this.isRollTitle ? 20 : this.showPageSize;
        payload.needTotal = this.realIsShowTotal;
        payload.needValidCount = this.realIsShowValid;
        payload.needNewCnt = this.realIsShowToday;
        this.useInterfaceGetArticleList(this.articleListInterface, payload);
      } else {
        const payload = {
          ...getParams(
            this.noticeTabList,
            this.statusParamsList,
            this.paramsDistrictCode
          ),
          pageSize: this.showPageSize,
          needTotal: this.realIsShowTotal,
          needValidCount: this.realIsShowValid,
          needNewCnt: this.realIsShowToday,
        };
        // 无tab栏是直接传递code即可
        this.useInterfaceGetArticleList(this.articleListInterface, payload);
      }
    },
    // 处理滚动动画
    handleScrolling() {
      const noticeItemHeight =
        this.$refs.noticeListRef.clientHeight + parseInt(this.lineSpacing);
      // 给组件添加style属性
      const rollingTime = noticeItemHeight / this.rollingSpeed + "s";
      this.$el.style.setProperty("--transformHeight", -noticeItemHeight + "px");
      // 在最后一项加从头开始的图片，实现无缝衔接滚动
      const liHtml = this.$refs.noticeListRef.innerHTML;
      this.$refs.noticeListRef.innerHTML += liHtml;
      this.$el.style.setProperty(
        "--rollingAnimation",
        `rolling ${rollingTime} linear infinite normal`
      );
    },
  },
};
</script>
<style lang="less">
// 暂时把样式编译后输出，后续主题配置需重新处理
@import "./style/index.less";
.notice-list {
  animation: var(--rollingAnimation);
  &:hover {
    animation-play-state: var(--hoverStatus) !important;
  }
}
@-webkit-keyframes rolling {
  /* Safari and Chrome */
  0% {
    -webkit-transform: translateY(0);
  }
  100% {
    -webkit-transform: translateY(var(--transformHeight));
  }
}
@keyframes rolling {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(var(--transformHeight));
  }
}
</style>
