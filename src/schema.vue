<template>
  <div :class="classes">
    <div class="clearfix">
      <!-- 配置导航 -->
      <a-tabs :default-active-key="activeTab">
        <a-tab-pane
          v-for="item in schemaConfig"
          :key="item.groupName"
          :v-if="item.config && item.config.length"
          :tab="item.groupName"
        >
          <!-- 标题Tab配置特殊处理 -->
          <TitleTabGroup
            v-if="item.groupName === '标题Tab配置'"
            :update-tab-list="updateTabList"
            :schema-config-item="item.config"
            :list-name="item.groupListName"
            :options-obj="optionsObj"
            :site-id="siteId"
            :domain="domain"
            :base-url="baseUrl"
            :form="form"
            :whole-tree="wholeTree"
          />

          <!-- 对导航栏配置进行特殊处理 -->
          <TabListGroup
            v-else-if="
              item.groupName === '导航栏配置' ||
                item.groupName === '状态参数列表配置'
            "
            :update-tab-list="updateTabList"
            :schema-config-item="item.config"
            :list-name="item.groupListName"
            :options-obj="optionsObj"
            :site-id="siteId"
            :domain="domain"
            :base-url="baseUrl"
            :form="form"
            :whole-tree="wholeTree"
          />

          <!-- 普通配置 -->
          <CommonGroup
            v-else
            :form="form"
            :schema-config-item="item.config"
            :options-obj="optionsObj"
            @handleChange="handleChange"
          />
        </a-tab-pane>
      </a-tabs>
    </div>
  </div>
</template>
<script>
import Vue from "vue";
import CommonGroup from "./schemaGroup/commonGroup.vue";
import TabListGroup from "./schemaGroup/tabListGroup.vue";
import TitleTabGroup from "./schemaGroup/titleTabGroup.vue";
import schemaConfig from "./schemaConfig";
import { getUrlParam } from "./tools";
if (process.env.NODE_ENV === "development") {
  Vue.config.devtools = true;
}
// schema组件如下内置属性
// 1.compInfo:
//   该属性当组件集成到页面之后负责接受当前组件的信息，里面包括组件的info，组件的gourp，组件的schema等等
// 2.lubanSdk:
//   该属性负责传递鲁班平台内置Sdk的方法，方便组件调用鲁班平台内部能力。lubanSdk.Page.registerPageCompsValidator方法用于注册组件验证到页面级别，其他的请私下对称
//
// Q&A
// 1.如何调用平台能力？
//   直接调用this.lubanSdk下面的方法，具体啥方法私下对称，后续鲁班会在模板侧做优化，会提供对应的详细文档和方便的开发工具，这期由于时间的问题暂不提供
// 2.如果注册组件验证方法到页面基本
//   this.lubanSdk.Page.registerPageCompsValidator方法用于注册组件验证到页面级别
//   this.lubanSdk.Page.registerPageCompsValidator(this.compInfo, (callback)=>{
//     callback()//没有错误的情况
//     callback('错啦')有错误的情况
//   })
// 3.如何更新配置面板内最新的models
//   this.$emit('commitModel', xxx) 方式就可以，鲁班会发现值的改变，直接出发事件给previewer

export default {
  name: "pc-wsg-ArticlePurchaseNoticeList-front-schema",
  components: {
    CommonGroup,
    TabListGroup,
    TitleTabGroup,
  },
  // 组件props
  props: {
    compInfo: {
      type: Object,
      default: () => {},
    },
    pageInfo: {
      type: Object,
      default: () => {},
    },
    pageComp: {
      type: Object,
      default: () => {},
    },
    lubanSdk: {
      type: Object,
      default: () => {},
    },
  },
  // 组件 state
  data() {
    return {
      form: {
        // 配置项
        rollingSpeed: 30,
        componentTitle: "111", // 组件标题
        moreText: "更多",
        newTagTime: 0, // new 标签展示时间
        defaultActiveTab: "", // 默认选中的导航栏
        showPageSize: 8, // 组件展示数据条数
        secondLevelPageHref: "/site/category", // 二级页链接地址
        detailPageHref: "/site/detail", // 详情页链接地址
        titleIconfontClassName: "", // 标题图标类名
        paramsDistrictCode: "", // 区划 code
        titleFontWeight: "500", // 标题文字字重
        titleFontSize: "22px", // 标题文字大小
        articleTitleFontSize: "18px",
        componentWidth: "920px", // 组件宽度
        componentHeight: "400px", // 组件高度
        isRollTitle: false,
        isHoverStopRolling: true,
        isShowFooter: true, // 是否展示底部
        isShowToday: true, // 是否展示今日数据
        isShowValid: false, // 是否展示有效数据
        isShowTotal: false, // 是否展示总数
        isShowTab: true, // 是否展示导航
        isShowDistrictName: false, // 是否展示文章标题前的区划
        isShowPurchaseMethod: false, // 是否展示文章标题前的采购方式
        isShowTitleIconfont: false, // 是否展示组件标题前的图标
        isShowArticleStatus: false, // 是否展示文章状态
        isShowPubTime: false, // 是否展示发布时间
        articleListInterface: "/portal/searchHome", // 文章列表接口
        // 新的嵌套结构
        defaultActiveTitleTab: "采购公告", // 默认选中的标题Tab
        titleTabList: [
          // 标题Tab列表（嵌套结构）
          {
            titleTabName: "采购公告",

            noticeTabList: [
              {
                tabName: "招标公告",
                tabCategoryCode: "anhuiCategory001",
                tabSubsetCodes: "ZcyAnnouncement001-1,ZcyAnnouncement001-2",
                secondCode: "TENDER_NOTICE",
              },
              {
                tabName: "中标公告",
                tabCategoryCode: "anhuiCategory002",
                tabSubsetCodes: "ZcyAnnouncement002-1",
                secondCode: "WINNING_NOTICE",
              },
            ],
          },
          {
            titleTabName: "政策法规",

            noticeTabList: [
              {
                tabName: "国家政策",
                tabCategoryCode: "anhuiCategory101",
                tabSubsetCodes: "ZcyAnnouncement101-1,ZcyAnnouncement101-2",
                secondCode: "NATIONAL_POLICY",
              },
              {
                tabName: "地方法规",
                tabCategoryCode: "anhuiCategory102",
                tabSubsetCodes: "ZcyAnnouncement102-1",
                secondCode: "LOCAL_REGULATION",
              },
            ],
          },
        ],
        // 保留原有字段用于兼容
        noticeTabList: [], // 导航栏列表（将被titleTabList替代）
        statusParamsList: [], // 状态参数列表
        backgroundImageUrl: "", // 标题背景图片
        topUpLabelType: "", // 置顶标签颜色类型
        topUpLabelText: "", // 置顶标签文字
        isShowTopUpLabel: false,
      },

      // 配置项需要的值
      optionsObj: {
        tabOptions: [], // 用于默认选中导航栏的下拉
        siteIdOptions: [], // 用于选择网站的下拉
        parentCodeOptions: [], // 用于父级节点选择的下拉
        childCodeOptions: [], // 用于子级节点选择的下拉
        rollingSpeedOptions: [
          {
            label: "快",
            value: "30",
          },
          {
            label: "较快",
            value: "20",
          },
          {
            label: "中",
            value: "15",
          },
          {
            label: "较慢",
            value: "10",
          },
          {
            label: "慢",
            value: "5",
          },
        ],
        titleFontSizeOptions: [
          // 用于文字大小的下拉
          {
            label: "16px",
            value: "16px",
          },
          {
            label: "18px",
            value: "18px",
          },
          {
            label: "20px",
            value: "20px",
          },
          {
            label: "22px",
            value: "22px",
          },
          {
            label: "24px",
            value: "24px",
          },
        ],
        fontWeightOptions: [
          // 用于字重选择的下拉
          {
            label: "400",
            value: "400",
          },
          {
            label: "500",
            value: "500",
          },
          {
            label: "600",
            value: "600",
          },
          {
            label: "700",
            value: "700",
          },
          {
            label: "800",
            value: "800",
          },
        ],
        articleTitleFontSizeOptions: [
          {
            label: "14px",
            value: "14px",
          },
          {
            label: "15px",
            value: "15px",
          },
          {
            label: "16px",
            value: "16px",
          },
        ],
        iconFontSizeOptions: [
          {
            label: "24px",
            value: "24px",
          },
          {
            label: "26px",
            value: "26px",
          },
          {
            label: "28px",
            value: "28px",
          },
        ],
        moreFontSizeOptions: [
          {
            label: "12px",
            value: "12px",
          },
          {
            label: "13px",
            value: "13px",
          },
          {
            label: "14px",
            value: "14px",
          },
        ],
        articleLineHeightOptions: [
          {
            label: "8px",
            value: "8px",
          },
          {
            label: "12px",
            value: "12px",
          },
          {
            label: "16px",
            value: "16px",
          },
        ],
        articleMarginTopOptions: [
          {
            label: "0px",
            value: "0px",
          },
          {
            label: "4px",
            value: "4px",
          },
          {
            label: "6px",
            value: "6px",
          },
          {
            label: "8px",
            value: "8px",
          },
          {
            label: "10px",
            value: "10px",
          },
          {
            label: "12px",
            value: "12px",
          },
          {
            label: "14px",
            value: "14px",
          },
        ],
        topUpLabelTypeOptions: [
          { label: "success", value: "success" },
          { label: "info", value: "info" },
          { label: "warning", value: "warning" },
          { label: "danger", value: "danger" },
        ],
      },

      // schema的配置数组
      schemaConfig,

      // 配置项默认选中的导航
      activeTab: "基础配置" || schemaConfig[0].groupName,
      domain: null,
      siteId: null,
      baseUrl: null,
      wholeTree: [],
    };
  },
  // 组件 state
  computed: {
    classes() {
      return ["lego-pc-wsg-ArticlePurchaseNoticeList-front-schema"];
    },
  },
  watch: {
    // 'compInfo.models': {
    //   handler(newVal, oldVal) {
    //     if (JSON.stringify(oldVal) !== JSON.stringify(newVal)) {
    //       this.form = newVal;
    //     }
    //   },
    //   deep: true,
    // },
    compInfo: {
      handler(compInfo, oldVal) {
        if (JSON.stringify(oldVal) !== JSON.stringify(compInfo)) {
          this.form = {
            ...(compInfo.models || {}),
          };
        }
      },
      immediate: true,
      deep: true,
    },

    form: {
      handler(newVal, oldVal) {
        if (JSON.stringify(oldVal) !== JSON.stringify(newVal)) {
          this.handleChange(newVal);
        }
      },
      deep: true,
    },
  },
  mounted() {
    // 组件发起请求统一使用 axios,最终由搭建统一处理
    // axios("/api/district/children").then(() => {});
    // 校验注册
    // this.handleChange(this.form);
    this.lubanSdk.Page.registerPageCompsValidator(
      this.pageInfo,
      this.compInfo,
      this.compValidator
    );
    this.getDomain();
  },
  methods: {
    handleChange(form) {
      this.form = form;
      this.$emit("commitModel", this.form);
    },

    getDomain() {
      const pageId = getUrlParam("pageId");
      axios({
        url: `/page/${pageId}`, // 捷捷调试/lego/api/v1/page/${pageId}    /page/${pageId}
        method: "get",
      }).then((res) => {
        const siteDomainsArray = res?.siteDomains[0]; // 捷捷调试res?.data?.siteDomains[0]    res?.siteDomains[0]
        const siteDomainProd = siteDomainsArray.filter((i) => {
          return i.env.includes("prod") && i.domain !== "" && i.domain !== "-";
        });
        const siteDomainNewsite = siteDomainsArray.filter((i) => {
          return i.env === "newsite" && i.domain !== "" && i.domain !== "-";
        });
        const siteDomainStaging = siteDomainsArray.filter((i) => {
          return (
            i.env.includes("staging") && i.domain !== "" && i.domain !== "-"
          );
        });
        const siteDomainTest = siteDomainsArray.filter((i) => {
          return i.domain !== "" && i.domain !== "-";
        });
        if (siteDomainProd.length < 1) {
          if (siteDomainNewsite.length < 1) {
            if (siteDomainStaging.length < 1) {
              this.domain = siteDomainTest[0].domain;
              this.baseUrl = "http://zxtmp.test.zcygov.cn/";
            } else {
              this.domain = siteDomainStaging[0].domain;
              this.baseUrl = "http://zxtmp-staging.zcygov.cn/";
            }
          } else {
            this.domain = siteDomainNewsite[0].domain;
            this.baseUrl = "https://zxtmp.zcygov.cn/";
          }
        } else {
          this.domain = siteDomainProd[0].domain;
          this.baseUrl = "https://zxtmp.zcygov.cn/";
        }
        this.getSiteId(this.domain);
      });
    },

    getSiteId(domain) {
      axios({
        baseURL: this.baseUrl,
        url: "/portal/zSiteId",
        method: "GET",
        params: {
          domain,
        },
      }).then((res) => {
        this.siteId = res?.data; // 捷捷调试res?.result?.data    res?.data
        this.getParentTree(this.siteId, domain);
      });
    },

    // 获取当前网站的父级栏目
    getParentTree(siteId, domain) {
      axios({
        baseURL: this.baseUrl,
        url: "/portal/zCategoryTreeFind",
        method: "GET",
        params: {
          siteId,
          domain,
        },
      }).then((res) => {
        this.$set(
          this.optionsObj,
          "parentCodeOptions",
          res?.data[0]?.children || [] // 捷捷调试res?.result?.data[0]?.children   res?.data[0]?.children
        );
        this.wholeTree = res?.data[0]?.children;
      });
    },

    // 更新高级配置时触发，通过 tabKey 判断是哪种高级配置
    updateTabList(tabKey, newVal) {
      if (tabKey === "titleTabList") {
        this.form.titleTabList = [];
      }
      if (tabKey === "titleTabList") {
        newVal.forEach((item, index) => {
          // 处理嵌套的 noticeTabList，将 tabSubsetCodes 数组转换回字符串
          const processedNoticeTabList = (item.noticeTabList || []).map(subTab => ({
            ...subTab,
            tabSubsetCodes: Array.isArray(subTab.tabSubsetCodes)
              ? subTab.tabSubsetCodes.join(",")
              : subTab.tabSubsetCodes || "",
          }));

          this.$set(this.form.titleTabList, index, {
            titleTabName: item.titleTabName || "",
            noticeTabList: processedNoticeTabList,
          });
          this.$set(this.optionsObj.titleTabOptions, index, {
            label: item.titleTabName,
            value: item.titleTabName,
          });
        });
      }
      if (tabKey === "noticeTabList") {
        this.form.noticeTabList = [];
        this.optionsObj.tabOptions = [];
      }
      if (tabKey === "noticeTabList") {
        newVal.forEach((item, index) => {
          this.$set(this.form.noticeTabList, index, {
            tabName: item.tabName || "",
            tabCategoryCode: item.tabCategoryCode || [],
            tabSubsetCodes: item.tabSubsetCodes || [],
            hideTabSubsetCodes: item.hideTabSubsetCodes || "",
            tabId: item.tabId || "",
          });
          // this.$set(this.optionsObj.tabOptions, index, {
          //   label: item.tabName,
          //   value: item.tabName,
          // });
        });
      }
      if (tabKey === "statusParamsList") {
        this.$set(this.form, "statusParamsList", newVal);
      }
      this.handleChange(this.form);
    },
    compValidator(callback) {
      if (!this.$refs.form) {
        callback();
        return true;
      }
      this.$refs.form.validate((valid, errorFields) => {
        if (!valid) {
          callback(errorFields);
        } else {
          callback();
        }
      });
    },
  },
};
</script>
<style lang="less">
// 暂时把样式编译后输出，后续主题配置需重新处理
@import "./style/schema.less";
</style>
