const Mock = require('mockjs');

module.exports = {
  '/portal/announcement': () =>
    Mock.mock({"success":true,"result":{"data":{"code":"AdministrativeRegulations","moduleName":"政策法规","firstId":66462,"children":[{"title":"政策法规-国家-文章1政策法规-国家-文章1政策法规-国家-文章1政策法规-国家-文章1政策法规-国家-文章1","articleId":18354312,"pubDate":1672123271000,"publishDateString":"2022-12-27","districtName":"青秀区","purchaseMethod":null,"cover":"https://dev.zcycdn.com/f2e-assets/7603e3c9-fea6-405a-b817-083de0503e10.png","urgentLevel":1,"remark":"","encryptId":"fFUZQumST75fJKZQp0hWdw=="},{"title":"政策法规-自治区-文章1","articleId":18354313,"pubDate":1672123261000,"publishDateString":"2022-12-27","districtName":"桂林市","purchaseMethod":null,"cover":"https://dev.zcycdn.com/f2e-assets/7603e3c9-fea6-405a-b817-083de0503e10.png","urgentLevel":1,"remark":"","encryptId":"vBs3Hs2CjXooRvYy7l+ayg=="}]}},"error":null}),
};
