@import "./mixin";
@import "./theme";

@prefix-pc-wsg-ArticlePurchaseNoticeList-front-cls: ~"@{lego-css-prefix}pc-wsg-ArticlePurchaseNoticeList-front-schema";

.@{prefix-pc-wsg-ArticlePurchaseNoticeList-front-cls} {
  height: 90%;
  overflow: auto;
  .wrap {
    background-color: #f9fafb;
  }
  .ant-form-item-label {
    font-weight: 500;
    overflow: visible;
    white-space: normal;
  }
  .tab-item {
    font-size: 16px;
    font-weight: 700;
    color: #333;
  }
  .btn-list {
    float: right;
    &:nth-child(n) {
      margin-right: 10px;
    }
  }
  .ant-upload {
    width: 80% !important;
  }
}
