import Vue from 'vue';
import Vuex from 'vuex';
import { upperCase } from '../tools';
Vue.use(Vuex);

/* 这里是vuex root层（不推荐使用）
 *
 * * nameSpaced （推荐）
 * 为避免命名冲突，默认以ComponentName 作为nameSpace
 * 命名示例：组件名为pc-luochen-test1-front 命名为 pcLuochenTest1Front（小驼峰）
 * 温馨提示：转小驼峰的方案在 ../tools/index.js 中
 * */
const moduleName = upperCase('pc-wsg-ArticlePurchaseNoticeList-front');
export default new Vuex.Store({
  modules: {
    [moduleName]: {
      namespaced: true,
      state() {
        return {
          message: '这是vuex的消息',
        };
      },
      getters: {},
      actions: {},
      mutations: {
        setState(state, payload) {
          const keys = Object.keys(payload);
          keys.forEach((key) => {
            if (!state[key]) {
              Vue.set(state, key, payload[key]);
            } else {
              state[key] = payload[key];
            }
          });
        },
      },
    },
  },
});
