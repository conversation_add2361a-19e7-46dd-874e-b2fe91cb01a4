{"extends": ["stylelint-config-standard", "stylelint-config-prettier"], "plugins": ["stylelint-declaration-block-no-ignored-properties"], "rules": {"plugin/declaration-block-no-ignored-properties": true, "indentation": 2, "no-eol-whitespace": true, "function-name-case": ["lower", {"ignoreFunctions": ["/colorPalette/"]}], "no-invalid-double-slash-comments": null, "no-descending-specificity": null, "declaration-empty-line-before": null}, "ignoreFiles": ["src/style/theme.less", "lib/style/*.less"]}